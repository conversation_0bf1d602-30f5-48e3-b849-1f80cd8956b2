FROM python:3.9.23-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgl1 \
    libx11-6 \
    libxext6 \
    libsm6 \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 拷贝项目代码和依赖
COPY requirements.txt .
RUN pip install --index-url https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
COPY . .

# 启动服务（Gunicorn）
CMD ["gunicorn", "--timeout", "120","-b", "0.0.0.0:5000", "app:app"]